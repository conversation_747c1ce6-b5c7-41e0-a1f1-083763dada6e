const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const db = require('../../database/models');
const Sequelize = require('sequelize');
const campaignValidation = require('../../validations/advertiser/campaignValidation');
const reportService = require("../../services/advertiser/reportService");

module.exports = {
    async campaignList(req, res) {
        try {
            const valid = await campaignValidation.getCampaignValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let campaignList = await reportService.getCampaignReportList(req, res);
            if (campaignList == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.SUCCESS
                );
            } else if (campaignList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    campaignList,
                    message.CAMPAIGNLIST,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async adsList(req, res) {
        try {
            const valid = await campaignValidation.getCampaignValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let campaignList = await reportService.getAdsReportData(req, res);
            if (campaignList == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.SUCCESS
                );
            } else if (campaignList) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    campaignList,
                    message.CAMPAIGNLIST,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async performanceCnt(req, res) {
        try {
            const valid = await campaignValidation.getCampaignValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }
            let performanceCnt = await reportService.performanceCnt(req, res);
            if (performanceCnt == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.SUCCESS
                );
            } else if (performanceCnt) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    performanceCnt,
                    message.GETPERFORMANCECOUNT,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },
    async exportCampaignAdsReport(req, res) {
        try {
            const valid = await campaignValidation.getCampaignValidation(req);
            if (valid.error) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    valid.error.details[0].message,
                    status.ERROR
                );
            }

            let exportData = await reportService.exportCampaignAdsReport(req, res);

            if (exportData == 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.SUCCESS
                );
            } else if (exportData) {
                // Check if client wants a specific file type
                const fileType = req.body.file_type;

                if (fileType === 'campaigns') {
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', `attachment; filename="${exportData.campaigns.filename}"`);
                    return res.send(exportData.campaigns.data);
                } else if (fileType === 'ads') {
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', `attachment; filename="${exportData.ads.filename}"`);
                    return res.send(exportData.ads.data);
                } else {
                    // Default: Create combined CSV with both sections
                    const combinedCsv = `CAMPAIGNS\n\n${exportData.campaigns.data}\n\n\nADS\n\n${exportData.ads.data}`;
                    const filename = `campaign_ads_report_${new Date().toISOString().split('T')[0]}.csv`;

                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                    return res.send(combinedCsv);
                }
            } else {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.SOMETHINGWENTWRONG,
                    status.ERROR
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                [],
                message.INTERNALSERVERERROR,
                status.ERROR
            );
        }
    },

};
