const Sequelize = require('sequelize');
const db = require('../../database/models');
const AdsModel = require('../../database/models').ads;
const campaignModel = require('../../database/models').campaign;
const AdsSegment = require('../../database/models').ads_segment;
const AdstUsers = require('../../database/models').ads_users;
const AdvertiserUserModel = require('../../database/models').ads_users;
const mail = require('../../helper/sendmail');
const { s3Upload } = require('../../middleware/multerAwsUpload');
const constant = require('../../config/constant');

function formatTimeTo24Hour(timeStr) {
    if (!timeStr) return null; // Handle null or undefined time strings

    // Ensure timeStr is a string before splitting
    timeStr = String(timeStr).trim();

    const parts = timeStr.split(' ');
    let time = parts[0];
    let period = parts.length > 1 ? parts[1].toLowerCase() : '';

    let [hours, minutes] = time.split(':');

    hours = parseInt(hours, 10); // Use radix 10 for parseInt
    minutes = parseInt(minutes, 10);

    if (period === 'pm' && hours < 12) {
        hours += 12;
    } else if (period === 'am' && hours === 12) {
        hours = 0; // Midnight (12 AM) becomes 00
    }

    // Pad with leading zeros if necessary
    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}:00`; // Append seconds
}

module.exports = {
    /**
     * Add a new ad
     * @param {Object} req - Request object
     * @returns {Object} - Created ad object
     */
    async add(req, res) {
        const {
            id,
            renew_flag=false,
            bar_id,
            objective,
            campaign_id,
            ad_title,
            ad_description,
            call_to_action,
            created_by_type,
            eligibility_type,
            combined_eligibility,
            save_card,
            created_by_id,
            start_date,
            end_date,
            start_time,
            end_time,
            daily_budget,
            segment_ids,
            user_ids,
            stripe_payment_id,
            state,
            city,
            suburb,
            invoice_no,
            call_to_action_url
        } = req.body || {};

        try {
            // Check if campaign exists
            const campaign = await db.campaign.findOne({
                where: {
                    id: req.body.campaign_id,
                    deleted_at: null
                }
            });

            if (!campaign) {
                console.log("Campaign not found:", req.body.campaign_id);
                return 0; // Campaign not found
            }

            const existingAd = await AdsModel.findOne({
                where: {
                    campaign_id: campaign_id,
                    created_by_type: created_by_type,
                    created_by_id: created_by_id,
                    ad_title: ad_title,
                    deleted_at: null
                }
            });

            if (existingAd && !id && !renew_flag) {
                console.log("Ad with this title already exists for this campaign and creator");
                return -1; // Ad already exists
            }
            
            // Added for renew process
            if(renew_flag && id){
                await AdsModel.update(
                    { end_date: end_date, daily_budget: daily_budget },
                    {
                        where: {
                            id: id
                        },
                        logging: console.log
                    }
                );
                const updatedAd = await AdsModel.findOne({ where: { id } });
                return updatedAd;
            }

            // Process file upload if exists
            let mediaUrl = null;
            if (req.file) {
                // Upload to AWS S3 instead of local storage
                const path = constant.AWSS3ADVERTISERFOLDER + req.file.originalname;
                await s3Upload(req.file, path);
                mediaUrl = path;
            }
            awsUrl = req.file?.originalname;


            const formattedStartTime = formatTimeTo24Hour(start_time);
            const formattedEndTime = formatTimeTo24Hour(end_time);

            // We no longer need to verify payment ID here
            // Payment verification is now handled in the controller
            // and payment information is stored in the payments table

            // Create the adData object without payment information
            const combinedEligibilityValue = combined_eligibility == "true" ? 1 : 0;
            const save_card_value = save_card == 'true' ? 1 : 0;
            const adData = {
                invoice_no: invoice_no,
                campaign_id: campaign_id,
                objective: objective,
                ad_title: ad_title,
                ad_description: ad_description || '',
                media_url: awsUrl,
                bar_id: created_by_type === 'venue' ? bar_id : null,
                call_to_action: call_to_action || 'learn_more',
                eligibility_type: eligibility_type || 'all_mytab_customers',
                combined_eligibility: combinedEligibilityValue,
                save_card: save_card_value,
                created_by_type: created_by_type,
                created_by_id: created_by_id,
                start_date: start_date || new Date(),
                end_date: end_date || new Date(),
                start_time: formattedStartTime,
                end_time: formattedEndTime,
                daily_budget: daily_budget || 0,
                stripe_payment_id: stripe_payment_id || '',
                status: 'active',
                state: state || null,
                city: city || null,
                suburb: suburb || null,
                call_to_action_url: call_to_action_url || null
            };

            const ads = await AdsModel.create(adData);

            let subjectAdmin = 'New Ads Submitted for Review';
            let mailbodyAdmin = '<div>' +
                '<p>Hi Team,</p>' +
                '<p>A new ads has been created and submitted for review.</p>' +
                '<p><strong>Account Details:</strong></p>' +
                '<ul>' +
                    '<li><strong>Ads Name:</strong> ' + ad_title + '</li>' +
                    '<li><strong>Ads Description:</strong> ' + ad_description + '</li>' +
                '</ul>' +                    
                '<p>If you have any questions or need additional information, feel free to contact support at ' +
                '<a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a>.</p>' +
                '<p>Thank you,<br />MyTab Advertiser Support<br />' +
                '<a href="www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p>' +
                '</div>';
            mail.sendmail(res, process.env.ADVERTISER_EMAILTO, subjectAdmin, mailbodyAdmin);

            if (
                (eligibility_type === 'your_venues_customers_segment' || eligibility_type === 'mytab_customer_segments') &&
                segment_ids &&
                segment_ids.length > 0
            ) {
                const segmentDiscountData = segment_ids.map((segment_id) => ({
                    segmentID: segment_id,
                    adsID: ads.id,
                    barID: bar_id
                }));

                await AdsSegment.bulkCreate(segmentDiscountData, {
                    // transaction
                });
            }

            if (
                eligibility_type === 'your_venues_customers' &&
                user_ids &&
                user_ids.length > 0
            ) {
                const userEligibilityData = user_ids.map((user_id) => ({
                    userID: user_id,
                    adsID: ads.id,
                    barID: bar_id
                }));

                await AdstUsers.bulkCreate(
                    userEligibilityData
                    // { transaction }
                );
            }

            return ads;
        } catch (error) {
            console.error("Error in adsService.add:", error);
            throw error;
        }
    },
    async deleteAd(req, res) {
        try {
            let ads = await AdsModel.findOne({
                where: {
                    created_by_type: req.body.type,
                    id: req.body.id,
                    deleted_at: null
                }
            });

            // email is exist
            if (!ads) {
                return 0;
            }

            // delete ad
            if (ads) {
                await AdsModel.update({
                    deleted_by_admin: 'No',
                    deleted_at: new Date()
                },
                    {
                        where: {
                            id: ads.id
                        }
                    }
                );
            }
            return 1;
        } catch (err) {
            console.log(err);
            throw err;
        }
    },
    async deleteCampaign(req, res) {
        try {
            let campaign = await campaignModel.findOne({
                where: {
                    created_by_type: req.body.type,
                    id: req.body.id,
                    deleted_at: null
                }
            });

            if (!campaign) {
                return 0;
            }

            // delete campaign
            if (campaign) {
                await campaignModel.destroy({
                    where: {
                        id: campaign.id
                    }
                });
            }
            return 1;
        } catch (err) {
            console.log(err);
            throw err;
        }
    },
    async updatePauseStatus(req, res) {
        try {
            let ads = await AdsModel.findOne({
                where: {
                    created_by_type: req.body.type,
                    id: req.body.id,
                    deleted_at: null
                }
            });

            // email is exist
            if (!ads) {
                return 0;
            }

            if (ads) {
                await AdsModel.update(
                    { pause_status: req.body.flag, pause_by_admin: 'No' },
                    {
                        where: {
                            id: req.body.id
                        },
                        logging: console.log
                    }
                );
            }
            return 1;
        } catch (err) {
            console.log(err);
            throw err;
        }
    },
};

