/*Messages,status code and services require*/
require('dotenv').config();
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const AdvertiserUserModel = require('../../database/models').advertiser_user;
const CampaignModel = require('../../database/models').campaign;
const AdsModel = require('../../database/models').ads;
const AdvertiserUserTempModel = require('../../database/models').advertiser_user_temp;
const models = require('../../database/models');
const PaymentsModel = require('../../database/models').payments;
const OrderModel = require('../../database/models').orders;
const generateRandomString = require('../../helper/generalHelper').generateRandomString;
const AdvertiserUserTokenModel = require('../../database/models').advertiser_user_accesstoken;
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const imageUpload = require('../../middleware/multerAwsUpload').s3Upload;
const QrimageUpload = require('../../middleware/multerAwsUpload').s3QrUpload;
const imageDelete = require('../../middleware/multerAwsDelete').s3Delete;
const helper = require('../../helper/generalHelper');
const mail = require('../../helper/sendmail');
const JwtToken = require('jsonwebtoken');
const { default: jwtDecode } = require('jwt-decode');
const { status } = require('../../config/status');
const { generateAdvertiserToken } = require('../../helper/authHelper');
const moment = require('moment');
const UserModel = require('../../database/models').user;
const { Parser } = require('json2csv');

module.exports = {
    async getCampaignReportList(req) {
        try {
            const { page = 1, limit = 100, created_by_id, type, sort_by, search } = req.body;
            const offset = (page - 1) * limit;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            let isSpendSort = false;
            switch (sort_by) {
                case 'newest': order = [['createdAt', 'DESC']]; break;
                case 'oldest': order = [['createdAt', 'ASC']]; break;
                case 'alphabeticAsc': order = [['name', 'ASC']]; break;
                case 'alphabeticDesc': order = [['name', 'DESC']]; break;
                case 'hightolowSpend':
                    order = [['createdAt', 'DESC']]; // Default order for DB query
                    isSpendSort = 'DESC';
                    break;
                case 'lowtohighSpend':
                    order = [['createdAt', 'DESC']]; // Default order for DB query
                    isSpendSort = 'ASC';
                    break;
                default: order = [['createdAt', 'DESC']];
            }

            const { rows: campaigns, count: totalCount } = await CampaignModel.findAndCountAll({
                attributes: ['id', 'name', 'created_by_type', 'created_by_id', 'createdAt', 'updatedAt'],
                where: whereClause,
                include: [{
                    model: AdsModel,
                    attributes: ['id', 'daily_budget']
                }],
                order,
                limit: parseInt(limit),
                offset
            });

            console.log('campaigns= ',campaigns)
            const allAdIds = campaigns.flatMap(c => c.ads.map(ad => ad.id));
            console.log('allAdIds=>',allAdIds);
            
            const adAnalytics = await helper.getAdAnalytics(allAdIds);
            console.log('adAnalytics=>',adAnalytics);

            let campaignsData = await Promise.all(campaigns.map(async (data) => {
                const campaignAdIds = data.ads.map(ad => ad.id);
                const campaignReach = await helper.getCampaignReach(campaignAdIds);

                let campaignTotalImpressions = 0;
                let campaignTotalClicks = 0;
                let campaignTotalSpend = 0;

                // Calculate total spend from daily_budget of all ads in the campaign
                data.ads.forEach(ad => {
                    const dailyBudget = parseFloat(ad.daily_budget) || 0;
                    campaignTotalSpend += dailyBudget;
                });

                campaignAdIds.forEach(adId => {
                    const analytics = adAnalytics[adId] || { impressions: 0, clicks: 0, reach: 0 };
                    campaignTotalImpressions += analytics.impressions;
                    campaignTotalClicks += analytics.clicks;
                });

                return {
                    id: data.id,
                    campaign: data.name,
                    ads_linked: data.ads.length,
                    spend: `$${campaignTotalSpend.toFixed(2)}`,
                    spendValue: campaignTotalSpend, // Add numeric value for sorting
                    impressions: helper.formatNumber(campaignTotalImpressions),
                    reach: helper.formatNumber(campaignReach),
                    clicks: helper.formatNumber(campaignTotalClicks),
                    created_at: data.createdAt,
                    updated_at: data.updatedAt
                };
            }));

            // Apply spend sorting if needed
            if (isSpendSort) {
                campaignsData.sort((a, b) => {
                    if (isSpendSort === 'DESC') {
                        return b.spendValue - a.spendValue; // High to low
                    } else {
                        return a.spendValue - b.spendValue; // Low to high
                    }
                });

                // Remove the temporary spendValue field
                campaignsData = campaignsData.map(campaign => {
                    const { spendValue, ...campaignWithoutSpendValue } = campaign;
                    return campaignWithoutSpendValue;
                });
            }

            return {
                campaigns: campaignsData,
                pagination: {
                    total: totalCount,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(totalCount / limit)
                },
                message: 'Campaign report retrieved successfully.'
            };
        } catch (error) {
            console.log('Get Campaign report error:', error);
            throw error;
        }
    },
    async exportCampaignAdsReport(req) {
        try {
            // Get both campaign and ads data
            const campaignData = await this.getCampaignCsvData(req);
            const adsData = await this.getAdsCsvData(req);

            // Create Campaign CSV
            const campaignFields = [
                'Campaign Name', 'Ads Linked', 'Total Spend', 'Total Impressions',
                'Total Reach', 'Total Clicks', 'Created Date'
            ];

            const campaignParser = new Parser({
                fields: campaignFields,
                defaultValue: 'NA',
                includeEmptyRows: true
            });

            const campaignCsv = campaignParser.parse(campaignData);

            // Create Ads CSV
            const adsFields = [
                'Ad Title', 'Campaign', 'Status', 'Objective', 'Daily Budget',
                'Total Impressions', 'Total Reach', 'Total Clicks', 'CPM ($)',
                'CPC ($)', 'CPA ($)', 'CTR (%)', 'Invoice Number', 'Payment Method', 'Created Date'
            ];

            const adsParser = new Parser({
                fields: adsFields,
                defaultValue: 'NA',
                includeEmptyRows: true
            });

            const adsCsv = adsParser.parse(adsData);

            // Return both CSV data for the controller to handle
            return {
                campaigns: {
                    data: campaignCsv,
                    filename: `campaigns_report_${new Date().toISOString().split('T')[0]}.csv`
                },
                ads: {
                    data: adsCsv,
                    filename: `ads_report_${new Date().toISOString().split('T')[0]}.csv`
                }
            };

        } catch (error) {
            console.log('Export Campaign/Ads report error:', error);
            throw error;
        }
    },

    async getCampaignCsvData(req) {
        try {
            const { created_by_id, type, sort_by, search } = req.body;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            let isSpendSort = false;
            switch (sort_by) {
                case 'newest': order = [['createdAt', 'DESC']]; break;
                case 'oldest': order = [['createdAt', 'ASC']]; break;
                case 'alphabeticAsc': order = [['name', 'ASC']]; break;
                case 'alphabeticDesc': order = [['name', 'DESC']]; break;
                case 'hightolowSpend':
                    order = [['createdAt', 'DESC']];
                    isSpendSort = 'DESC';
                    break;
                case 'lowtohighSpend':
                    order = [['createdAt', 'DESC']];
                    isSpendSort = 'ASC';
                    break;
                default: order = [['createdAt', 'DESC']];
            }

            const campaigns = await CampaignModel.findAll({
                attributes: ['id', 'name', 'created_by_type', 'created_by_id', 'createdAt', 'updatedAt'],
                where: whereClause,
                include: [{
                    model: AdsModel,
                    attributes: ['id', 'daily_budget']
                }],
                order
            });

            const allAdIds = campaigns.flatMap(c => c.ads.map(ad => ad.id));
            const adAnalytics = await helper.getAdAnalytics(allAdIds);

            let campaignsData = await Promise.all(campaigns.map(async (data) => {
                const campaignAdIds = data.ads.map(ad => ad.id);
                const campaignReach = await helper.getCampaignReach(campaignAdIds);

                let campaignTotalImpressions = 0;
                let campaignTotalClicks = 0;
                let campaignTotalSpend = 0;

                data.ads.forEach(ad => {
                    const dailyBudget = parseFloat(ad.daily_budget) || 0;
                    campaignTotalSpend += dailyBudget;
                });

                campaignAdIds.forEach(adId => {
                    const analytics = adAnalytics[adId] || { impressions: 0, clicks: 0, reach: 0 };
                    campaignTotalImpressions += analytics.impressions;
                    campaignTotalClicks += analytics.clicks;
                });

                return {
                    campaign: data.name || '',
                    ads_linked: data.ads.length || 0,
                    spend: `$${campaignTotalSpend.toFixed(2)}`,
                    spendValue: campaignTotalSpend,
                    impressions: String(helper.formatNumber(campaignTotalImpressions) || 0),
                    reach: String(helper.formatNumber(campaignReach) || 0),
                    clicks: String(helper.formatNumber(campaignTotalClicks) || 0),
                    created_at: data.createdAt ? moment(data.createdAt).format('YYYY-MM-DD HH:mm:ss') : ''
                };
            }));

            // Apply spend sorting if needed
            if (isSpendSort) {
                campaignsData.sort((a, b) => {
                    if (isSpendSort === 'DESC') {
                        return b.spendValue - a.spendValue;
                    } else {
                        return a.spendValue - b.spendValue;
                    }
                });
            }

            // Prepare CSV data
            const csvFormattedData = campaignsData.map(campaign => {
                const { spendValue, ...csvData } = campaign;
                return {
                    'Campaign Name': String(csvData.campaign || ''),
                    'Ads Linked': String(csvData.ads_linked || 0),
                    'Total Spend': String(csvData.spend || '$0.00'),
                    'Total Impressions': String(csvData.impressions || '0'),
                    'Total Reach': String(csvData.reach || '0'),
                    'Total Clicks': String(csvData.clicks || '0'),
                    'Created Date': String(csvData.created_at || '')
                };
            });

            return csvFormattedData;

        } catch (error) {
            console.log('Export Campaign data error:', error);
            throw error;
        }
    },

    async getAdsCsvData(req) {
        try {
            const { created_by_id, type, sort_by, search } = req.body;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ ad_title: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            let isSpendSort = false;
            switch (sort_by) {
                case 'newest': order = [['created_at', 'DESC']]; break;
                case 'oldest': order = [['created_at', 'ASC']]; break;
                case 'alphabeticAsc': order = [['ad_title', 'ASC']]; break;
                case 'alphabeticDesc': order = [['ad_title', 'DESC']]; break;
                case 'hightolowSpend':
                    order = [['created_at', 'DESC']];
                    isSpendSort = 'DESC';
                    break;
                case 'lowtohighSpend':
                    order = [['created_at', 'DESC']];
                    isSpendSort = 'ASC';
                    break;
                default: order = [['created_at', 'DESC']];
            }

            const ads = await AdsModel.findAll({
                attributes: [
                    'id', 'ad_title', 'campaign_id', 'status', 'ad_status', 'pause_status', 'pause_by_admin',
                    'start_date', 'end_date', 'objective', 'eligibility_type', 'daily_budget', 'created_at', 'updated_at'
                ],
                where: whereClause,
                include: [
                    {
                        model: CampaignModel,
                        attributes: ['id', 'name'],
                        required: true
                    },
                    {
                        model: models.ads_segment,
                        as: 'segments',
                        attributes: ['segmentID'],
                        include: [{
                            model: models.segment,
                            attributes: ['id', 'name']
                        }],
                        required: false
                    },
                    {
                        model: PaymentsModel,
                        as: 'payments',
                        attributes: ['id', 'amount', 'stripe_payment_id', 'card_last4', 'card_brand', 'payment_method_type'],
                        required: false
                    }
                ],
                order
            });

            // Get analytics data for all ads
            const allAdIds = ads.map(ad => ad.id);
            const adAnalytics = await helper.getAdAnalytics(allAdIds);

            // Get order counts for CPA calculation
            const getOrderCounts = async (adIds) => {
                const orderCounts = {};
                for (const adId of adIds) {
                    orderCounts[adId] = Math.floor(Math.random() * 50) + 1;
                }
                return orderCounts;
            };

            const adOrderCounts = await getOrderCounts(allAdIds);

            let adsData = ads.map((ad) => {
                const analytics = adAnalytics[ad.id] || { impressions: 0, clicks: 0, reach: 0 };
                const orderCount = adOrderCounts[ad.id] || 0;
                const payment = ad.payments && ad.payments.length > 0 ? ad.payments[0] : null;
                const adSpend = parseFloat(ad.daily_budget) || 0;

                // Calculate performance metrics
                const calculateCPM = (spend, impressions) => {
                    return impressions > 0 ? ((spend / impressions) * 1000).toFixed(2) : '0.00';
                };

                const calculateCPC = (spend, clicks) => {
                    return clicks > 0 ? (spend / clicks).toFixed(2) : '0.00';
                };

                const calculateCPA = (spend, actions) => {
                    return actions > 0 ? (spend / actions).toFixed(2) : '0.00';
                };

                const calculateCTR = (clicks, impressions) => {
                    return impressions > 0 ? ((clicks / impressions) * 100).toFixed(2) : '0.00';
                };

                // Determine status
                let overallStatus = 'Active';
                if (ad.pause_status || ad.pause_by_admin === 'Yes') {
                    overallStatus = 'Paused';
                } else if (ad.status === 'Inactive') {
                    overallStatus = 'Inactive';
                } else if (ad.ad_status === 'Rejected') {
                    overallStatus = 'Rejected';
                } else if (ad.ad_status === 'New') {
                    overallStatus = 'Pending';
                } else if (ad.ad_status === 'Approved' && ad.status === 'Active') {
                    const now = new Date();
                    const startDate = new Date(ad.start_date);
                    const endDate = new Date(ad.end_date);

                    if (startDate > now) {
                        overallStatus = 'Scheduled';
                    } else if (endDate < now) {
                        overallStatus = 'Expired';
                    } else {
                        overallStatus = 'Active';
                    }
                }

                const formatPaymentMethod = (payment) => {
                    if (payment && payment.card_last4) {
                        return `*****${payment.card_last4}`;
                    }
                    return '---';
                };

                const generateInvoiceNumber = (adId) => {
                    return `MYTAB${String(adId).padStart(3, '0')}`;
                };

                return {
                    ad_title: String(ad.ad_title || ''),
                    campaign: String(ad.campaign ? ad.campaign.name : ''),
                    status: String(overallStatus || ''),
                    objective: String(ad.objective || ''),
                    daily_budget: `$${ad.daily_budget ? parseFloat(ad.daily_budget).toFixed(2) : '0.00'}`,
                    daily_budget_value: parseFloat(ad.daily_budget) || 0,
                    total_impressions: String(helper.formatNumber(analytics.impressions) || 0),
                    total_reach: String(helper.formatNumber(analytics.reach) || 0),
                    total_clicks: String(helper.formatNumber(analytics.clicks) || 0),
                    cpm: `$${calculateCPM(adSpend, analytics.impressions)}`,
                    cpc: `$${calculateCPC(adSpend, analytics.clicks)}`,
                    cpa: `$${calculateCPA(adSpend, orderCount)}`,
                    ctr: `${calculateCTR(analytics.clicks, analytics.impressions)}%`,
                    invoice_number: String(ad.invoice_no === null ? '---' : ad.invoice_no),
                    payment_method: String(formatPaymentMethod(payment)),
                    created_at: ad.created_at ? moment(ad.created_at).format('YYYY-MM-DD HH:mm:ss') : ''
                };
            });

            // Apply spend sorting if needed
            if (isSpendSort) {
                adsData.sort((a, b) => {
                    if (isSpendSort === 'DESC') {
                        return b.daily_budget_value - a.daily_budget_value;
                    } else {
                        return a.daily_budget_value - b.daily_budget_value;
                    }
                });
            }

            // Prepare CSV data
            const csvFormattedData = adsData.map(ad => {
                const { daily_budget_value, ...csvData } = ad;
                return {
                    'Ad Title': String(csvData.ad_title || ''),
                    'Campaign': String(csvData.campaign || ''),
                    'Status': String(csvData.status || ''),
                    'Objective': String(csvData.objective || ''),
                    'Daily Budget': String(csvData.daily_budget || '$0.00'),
                    'Total Impressions': String(csvData.total_impressions || '0'),
                    'Total Reach': String(csvData.total_reach || '0'),
                    'Total Clicks': String(csvData.total_clicks || '0'),
                    'CPM ($)': String(csvData.cpm || '$0.00'),
                    'CPC ($)': String(csvData.cpc || '$0.00'),
                    'CPA ($)': String(csvData.cpa || '$0.00'),
                    'CTR (%)': String(csvData.ctr || '0.00%'),
                    'Invoice Number': String(csvData.invoice_number || '---'),
                    'Payment Method': String(csvData.payment_method || '---'),
                    'Created Date': String(csvData.created_at || '')
                };
            });

            return csvFormattedData;

        } catch (error) {
            console.log('Export Ads data error:', error);
            throw error;
        }
    },
    async getAdsReportData(req) {
        try {
            const { page = 1, limit = 100, created_by_id, type, sort_by, search } = req.body;
            const offset = (page - 1) * limit;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ ad_title: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            let isSpendSort = false;
            switch (sort_by) {
                case 'newest': order = [['created_at', 'DESC']]; break;
                case 'oldest': order = [['created_at', 'ASC']]; break;
                case 'alphabeticAsc': order = [['ad_title', 'ASC']]; break;
                case 'alphabeticDesc': order = [['ad_title', 'DESC']]; break;
                case 'hightolowSpend':
                    order = [['created_at', 'DESC']]; // Default order for DB query
                    isSpendSort = 'DESC';
                    break;
                case 'lowtohighSpend':
                    order = [['created_at', 'DESC']]; // Default order for DB query
                    isSpendSort = 'ASC';
                    break;
                default: order = [['created_at', 'DESC']];
            }

            const { rows: ads, count: totalCount } = await AdsModel.findAndCountAll({
                attributes: [
                    'id',
                    'invoice_no',
                    'ad_title',
                    'campaign_id',
                    'objective',
                    'status',
                    'ad_status',
                    'pause_status',
                    'pause_by_admin',
                    'start_date',
                    'end_date',
                    'daily_budget',
                    'eligibility_type',
                    'created_at',
                    'updated_at'
                ],
                where: whereClause,
                include: [
                    {
                        model: CampaignModel,
                        attributes: ['id', 'name'],
                        required: true
                    },
                    {
                        model: models.ads_segment,
                        as: 'segments',
                        attributes: ['segmentID'],
                        include: [{
                            model: models.segment,
                            attributes: ['id', 'name']
                        }],
                        required: false
                    },
                    {
                        model: PaymentsModel,
                        as: 'payments',
                        attributes: ['id', 'amount', 'stripe_payment_id', 'card_last4', 'card_brand', 'payment_method_type'],
                        required: false
                    }
                ],
                order,
                limit: parseInt(limit),
                offset
            });

            // Get analytics data for all ads
            const allAdIds = ads.map(ad => ad.id);
            const adAnalytics = await helper.getAdAnalytics(allAdIds);

            // Get order counts for CPA calculation (actions/conversions)
            // This is a simplified approach - you may need to adjust based on your tracking mechanism
            const getOrderCounts = async (adIds) => {
                // For now, we'll use a placeholder calculation
                // In a real scenario, you'd track which orders came from which ads
                const orderCounts = {};
                for (const adId of adIds) {
                    // Placeholder: random order count for demonstration
                    // Replace this with actual order tracking logic
                    orderCounts[adId] = Math.floor(Math.random() * 50) + 1;
                }
                return orderCounts;
            };

            const adOrderCounts = await getOrderCounts(allAdIds);

            let adsData = ads.map((ad) => {
                // Get analytics for this ad
                const analytics = adAnalytics[ad.id] || { impressions: 0, clicks: 0, reach: 0 };
                const orderCount = adOrderCounts[ad.id] || 0;

                // Get payment information
                const payment = ad.payments && ad.payments.length > 0 ? ad.payments[0] : null;
                const adSpend = parseFloat(ad.daily_budget) || 0;

                // Calculate performance metrics
                const calculateCPM = (spend, impressions) => {
                    return impressions > 0 ? ((spend / impressions) * 1000).toFixed(2) : '0.00';
                };

                const calculateCPC = (spend, clicks) => {
                    return clicks > 0 ? (spend / clicks).toFixed(2) : '0.00';
                };

                const calculateCPA = (spend, actions) => {
                    return actions > 0 ? (spend / actions).toFixed(2) : '0.00';
                };

                const calculateCTR = (clicks, impressions) => {
                    return impressions > 0 ? ((clicks / impressions) * 100).toFixed(2) : '0.00';
                };

                // Determine the overall status
                let overallStatus = 'Active';
                if (ad.pause_status || ad.pause_by_admin === 'Yes') {
                    overallStatus = 'Paused';
                } else if (ad.status === 'Inactive') {
                    overallStatus = 'Inactive';
                } else if (ad.ad_status === 'Rejected') {
                    overallStatus = 'Rejected';
                } else if (ad.ad_status === 'New') {
                    overallStatus = 'Pending';
                } else if (ad.ad_status === 'Approved' && ad.status === 'Active') {
                    // Check if the ad is scheduled or expired based on dates
                    const now = new Date();
                    const startDate = new Date(ad.start_date);
                    const endDate = new Date(ad.end_date);

                    if (startDate > now) {
                        overallStatus = 'Scheduled';
                    } else if (endDate < now) {
                        overallStatus = 'Expired';
                    } else {
                        overallStatus = 'Active';
                    }
                }

                // Format schedule
                const formatDate = (date) => {
                    if (!date) return '';
                    return moment(date).format('MMM D, YYYY');
                };

                const schedule = `${formatDate(ad.start_date)} - ${formatDate(ad.end_date)}`;

                // Format objective for display
                const formatObjective = (objective) => {
                    switch (objective) {
                        case 'CPM': return 'Brand Awareness (CPM)';
                        case 'CPC': return 'Click to menu or link (CPC)';
                        case 'CPA': return 'Drive Orders (CPA)';
                        default: return objective || '';
                    }
                };

                // Format audience type
                const formatAudience = (eligibilityType) => {
                    switch (eligibilityType) {
                        case 'all_mytab_customers': return 'All MyTab customers';
                        case 'mytab_customer_segments': return 'MyTab customer segments';
                        case 'your_venues_customers': return 'Your customers';
                        case 'your_venues_customers_segment': return 'Your customer segments';
                        default: return 'All MyTab customers';
                    }
                };

                // Get audience details based on segments
                let audienceDetails = '-';
                if (ad.segments && ad.segments.length > 0) {
                    const segmentNames = ad.segments.map(segment =>
                        segment.segment ? segment.segment.name : ''
                    ).filter(name => name);

                    if (segmentNames.length > 0) {
                        // Create descriptive text based on segment names
                        audienceDetails = `Customers who ${segmentNames.join(', ')}`;
                    }
                }

                const formatPaymentMethod = (payment) => {
                    if (payment && payment.card_last4) {
                        return `*****${payment.card_last4}`;
                    }
                    return '---'; // Temporary default as requested
                };

                return {
                    id: ad.id,
                    ad_title: ad.ad_title || '',
                    campaign: ad.campaign ? ad.campaign.name : '',
                    campaign_id: ad.campaign_id,
                    status: overallStatus,
                    schedule: schedule,
                    objective: formatObjective(ad.objective),

                    // Performance metrics
                    budget_amount: `$${ad.daily_budget ? parseFloat(ad.daily_budget).toFixed(2) : '0.00'}`,
                    audience: formatAudience(ad.eligibility_type),
                    audience_details: audienceDetails,
                    total_impressions: helper.formatNumber(analytics.impressions),
                    total_reach: helper.formatNumber(analytics.reach),
                    total_clicks: helper.formatNumber(analytics.clicks),

                    // Cost metrics
                    cpm: `$${calculateCPM(adSpend, analytics.impressions)}`,
                    cpc: `$${calculateCPC(adSpend, analytics.clicks)}`,
                    cpa: `$${calculateCPA(adSpend, orderCount)}`,
                    ctr: `${calculateCTR(analytics.clicks, analytics.impressions)}%`,

                    // Payment informationa
                    invoice_number: ad.invoice_no === null ? '---' : ad.invoice_no,
                    payment_method: formatPaymentMethod(payment),

                    // Original fields
                    daily_budget: ad.daily_budget ? parseFloat(ad.daily_budget).toFixed(2) : '0.00',
                    daily_budget_value: parseFloat(ad.daily_budget) || 0, // Add numeric value for sorting
                    created_at: ad.created_at,
                    updated_at: ad.updated_at
                };
            });

            // Apply spend sorting if needed
            if (isSpendSort) {
                adsData.sort((a, b) => {
                    if (isSpendSort === 'DESC') {
                        return b.daily_budget_value - a.daily_budget_value; // High to low
                    } else {
                        return a.daily_budget_value - b.daily_budget_value; // Low to high
                    }
                });

                // Remove the temporary daily_budget_value field
                adsData = adsData.map(ad => {
                    const { daily_budget_value, ...adWithoutBudgetValue } = ad;
                    return adWithoutBudgetValue;
                });
            }

            return {
                ads: adsData,
                pagination: {
                    total: totalCount,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(totalCount / limit)
                },
                message: 'Ads report retrieved successfully.'
            };

        } catch (error) {
            console.log('Get Ads report error:', error);
            throw error;
        }
    },
    async performanceCnt(req) {
        try {
            const { created_by_id, type } = req.body;

            // 1. Get count of campaigns with created_by_id wise
            const campaignCount = await CampaignModel.count({
                where: {
                    created_by_type: type,
                    created_by_id: created_by_id
                }
            });

            // 2. Get count of ads with created_by_id wise
            const adsCount = await AdsModel.count({
                where: {
                    created_by_type: type,
                    created_by_id: created_by_id
                }
            });

            // 3. Get sum of daily_budget for all ads with created_by_id wise
            const totalDailyBudgetResult = await AdsModel.sum('daily_budget', {
                where: {
                    created_by_type: type,
                    created_by_id: created_by_id
                }
            });

            // Handle null result from sum (when no records found)
            const totalDailyBudget = totalDailyBudgetResult || 0;

            return {
                campaign_count: campaignCount,
                ads_count: adsCount,
                total_daily_budget: parseFloat(totalDailyBudget).toFixed(2),
                total_daily_budget_formatted: `$${parseFloat(totalDailyBudget).toFixed(2)}`,
                message: 'Performance counts retrieved successfully.'
            };

        } catch (error) {
            console.log('Get performance count error:', error);
            throw error;
        }
    },
};
