/*Messages,status code and services require*/
require('dotenv').config();
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const constant = require('../../config/constant');
const AdvertiserUserModel = require('../../database/models').advertiser_user;
const CampaignModel = require('../../database/models').campaign;
const AdsModel = require('../../database/models').ads;
const AdvertiserUserTempModel = require('../../database/models').advertiser_user_temp;
const models = require('../../database/models');
const generateRandomString = require('../../helper/generalHelper').generateRandomString;
const AdvertiserUserTokenModel = require('../../database/models').advertiser_user_accesstoken;
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const imageUpload = require('../../middleware/multerAwsUpload').s3Upload;
const QrimageUpload = require('../../middleware/multerAwsUpload').s3QrUpload;
const imageDelete = require('../../middleware/multerAwsDelete').s3Delete;
const helper = require('../../helper/generalHelper');
const mail = require('../../helper/sendmail');
const JwtToken = require('jsonwebtoken');
const { default: jwtDecode } = require('jwt-decode');
const { status } = require('../../config/status');
const { generateAdvertiserToken } = require('../../helper/authHelper');
const moment = require('moment');
const UserModel = require('../../database/models').user;

module.exports = {
    async getCampaignReportList(req) {
        try {
            const { page = 1, limit = 100, created_by_id, type, sort_by, search } = req.body;
            const offset = (page - 1) * limit;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            switch (sort_by) {
                case 'newest': order = [['createdAt', 'DESC']]; break;
                case 'oldest': order = [['createdAt', 'ASC']]; break;
                case 'alphabeticAsc': order = [['name', 'ASC']]; break;
                case 'alphabeticDesc': order = [['name', 'DESC']]; break;
                default: order = [['createdAt', 'DESC']];
            }

            const { rows: campaigns, count: totalCount } = await CampaignModel.findAndCountAll({
                attributes: ['id', 'name', 'created_by_type', 'created_by_id', 'createdAt', 'updatedAt'],
                where: whereClause,
                include: [{
                    model: AdsModel,
                    attributes: ['id', 'daily_budget']
                }],
                order,
                limit: parseInt(limit),
                offset
            });

            console.log('campaigns= ',campaigns)
            const allAdIds = campaigns.flatMap(c => c.ads.map(ad => ad.id));
            console.log('allAdIds=>',allAdIds);
            
            const adAnalytics = await helper.getAdAnalytics(allAdIds);
            console.log('adAnalytics=>',adAnalytics);

            return {
                campaigns: await Promise.all(campaigns.map(async (data) => {
                    const campaignAdIds = data.ads.map(ad => ad.id);
                    const campaignReach = await helper.getCampaignReach(campaignAdIds);

                    let campaignTotalImpressions = 0;
                    let campaignTotalClicks = 0;
                    let campaignTotalSpend = 0;

                    // Calculate total spend from daily_budget of all ads in the campaign
                    data.ads.forEach(ad => {
                        const dailyBudget = parseFloat(ad.daily_budget) || 0;
                        campaignTotalSpend += dailyBudget;
                    });

                    campaignAdIds.forEach(adId => {
                        const analytics = adAnalytics[adId] || { impressions: 0, clicks: 0, reach: 0 };
                        campaignTotalImpressions += analytics.impressions;
                        campaignTotalClicks += analytics.clicks;
                    });

                    return {
                        id: data.id,
                        campaign: data.name,
                        ads_linked: data.ads.length,
                        spend: `$${campaignTotalSpend.toFixed(2)}`,
                        impressions: helper.formatNumber(campaignTotalImpressions),
                        reach: helper.formatNumber(campaignReach),
                        clicks: helper.formatNumber(campaignTotalClicks),
                        created_at: data.createdAt,
                        updated_at: data.updatedAt
                    };
                })),
                pagination: {
                    total: totalCount,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(totalCount / limit)
                },
                message: 'Campaign report retrieved successfully.'
            };
        } catch (error) {
            console.log('Get Campaign report error:', error);
            throw error;
        }
    },
    async performanceCnt(req) {
        try {
            const { page = 1, limit = 100, created_by_id, type, sort_by, search } = req.body;
            const offset = (page - 1) * limit;

            const whereClause = { created_by_type: type, created_by_id };

            if (search && search.trim()) {
                whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
            }

            let order;
            switch (sort_by) {
                case 'newest': order = [['createdAt', 'DESC']]; break;
                case 'oldest': order = [['createdAt', 'ASC']]; break;
                case 'alphabeticAsc': order = [['name', 'ASC']]; break;
                case 'alphabeticDesc': order = [['name', 'DESC']]; break;
                default: order = [['createdAt', 'DESC']];
            }

            const { rows: campaigns, count: totalCount } = await CampaignModel.findAndCountAll({
                attributes: ['id', 'name', 'created_by_type', 'created_by_id', 'createdAt', 'updatedAt'],
                where: whereClause,
                include: [{
                    model: AdsModel,
                    attributes: ['id', 'daily_budget']
                }],
                order,
                limit: parseInt(limit),
                offset
            });

            console.log('campaigns= ',campaigns)
            const allAdIds = campaigns.flatMap(c => c.ads.map(ad => ad.id));
            console.log('allAdIds=>',allAdIds);
            
            const adAnalytics = await helper.getAdAnalytics(allAdIds);
            console.log('adAnalytics=>',adAnalytics);

            return {
                campaigns: await Promise.all(campaigns.map(async (data) => {
                    const campaignAdIds = data.ads.map(ad => ad.id);
                    const campaignReach = await helper.getCampaignReach(campaignAdIds);

                    let campaignTotalImpressions = 0;
                    let campaignTotalClicks = 0;
                    let campaignTotalSpend = 0;

                    // Calculate total spend from daily_budget of all ads in the campaign
                    data.ads.forEach(ad => {
                        const dailyBudget = parseFloat(ad.daily_budget) || 0;
                        campaignTotalSpend += dailyBudget;
                    });

                    campaignAdIds.forEach(adId => {
                        const analytics = adAnalytics[adId] || { impressions: 0, clicks: 0, reach: 0 };
                        campaignTotalImpressions += analytics.impressions;
                        campaignTotalClicks += analytics.clicks;
                    });

                    return {
                        id: data.id,
                        campaign: data.name,
                        ads_linked: data.ads.length,
                        spend: `$${campaignTotalSpend.toFixed(2)}`,
                        impressions: helper.formatNumber(campaignTotalImpressions),
                        reach: helper.formatNumber(campaignReach),
                        clicks: helper.formatNumber(campaignTotalClicks),
                        created_at: data.createdAt,
                        updated_at: data.updatedAt
                    };
                })),
                pagination: {
                    total: totalCount,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(totalCount / limit)
                },
                message: 'Campaign report retrieved successfully.'
            };
        } catch (error) {
            console.log('Get Campaign report error:', error);
            throw error;
        }
    },
};
