// routes/shared/adsRoutes.js
const express = require('express');
const advertiserAuth = require('../../middleware/advertiserAuth').advertiserAuthorization;
const venueAuth = require("../../middleware/venueAuth").venueAuthorization;
const MulterMiddleware = require('../../middleware/multer');
const ReportController = require('../../controllers/advertiser/ReportController');

const createReportsRoutes = (userType) => {
    const router = express.Router();
    
    // Choose auth middleware based on user type
    const auth = userType === 'venue' ? venueAuth : advertiserAuth;

    // Routes
    router.get('/test', (req, res) => res.json({ message: `${userType} ads working` }));
    router.post('/list', auth, ReportController.list);
    return router;
};

module.exports = createReportsRoutes;