const adsService = require('../../services/advertiser/adservices');
const adsValidation = require('../../validations/advertiser/adsvalidation');
const message = require('../../config/cmsMessage').cmsMessage;
const status = require('../../config/status').status;
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-04-30.basil'
});
const db = require('../../database/models');
const Sequelize = require('sequelize');
const VenueModel = require('../../database/models').venue_user; //import modal always Capital
const AdsModel = require('../../database/models').advertiser_user;
const PaymentsModel = require('../../database/models').payments;

// Add card method - simplified
async function processPayment({ payment_method_id, customer_id, amount, description, save_card = false }) {
    try {
        const amountInCents = Math.round(parseFloat(amount) * 100);
        const finalAmount = Math.max(amountInCents, 50); // Minimum 50 cents

        if (save_card) {
            await stripe.paymentMethods.attach(payment_method_id, {
                customer: customer_id
            });
        }

        const paymentIntent = await stripe.paymentIntents.create({
            amount: finalAmount,
            currency: 'usd',
            customer: customer_id,
            payment_method: payment_method_id,
            description,
            confirm: true,
            setup_future_usage: save_card ? 'off_session' : undefined,
            automatic_payment_methods: {
                enabled: true,
                allow_redirects: 'never'
            },
            // REMOVE THIS LINE: payment_method_types: ['card']
        });

        if (paymentIntent.status === 'succeeded') {
            return {
                success: true,
                payment_intent_id: paymentIntent.id,
                status: paymentIntent.status,
                card_saved: save_card
            };
        } else {
            return {
                success: false,
                error: `Payment failed with status: ${paymentIntent.status}`
            };
        }

    } catch (error) {
        console.error("Process payment error:", error);
        return {
            success: false,
            error: error.message
        };
    }
}
async function getOrCreateCustomer(created_by_id, created_by_type) {
    const existingCustomer = await PaymentsModel.findOne({
        where: {
            created_by_id: created_by_id, // Ensure this matches the argument
            created_by_type: created_by_type, // Ensure this matches the argument
            stripe_customer_id: { [Sequelize.Op.not]: null }
        },
        attributes: ['stripe_customer_id']
    });
    console.log(existingCustomer, "existingCustomer")
    if (existingCustomer) {
        return existingCustomer.stripe_customer_id;
    }

    // Get user details for customer creation
    let userEmail = '';
    if (created_by_type === 'venue') {
        const venue = await VenueModel.findByPk(created_by_id);
        userEmail = venue?.email || '';
    } else if (created_by_type === 'advertiser') {
        const advertiser = await AdsModel.findByPk(created_by_id);
        userEmail = advertiser?.email || '';
    }

    const customer = await stripe.customers.create({
        email: userEmail,
        description: `${created_by_type} - ${created_by_id}`
    });

    // Save customer to database
    await db.payments.create({
        created_by_id,
        created_by_type,
        stripe_customer_id: customer.id,
        payment_method_type: 'card',
        amount: 0,
        payment_status: 'customer_created',
        stripe_payment_id: ''
    });

    return customer.id;
}
async function processPaymentForAd(paymentData) {
    const {
        saved_card_id,
        customer_id,
        amount,
        ad_title,
        created_by_id,
        created_by_type
    } = paymentData;

    try {
        const amountInCents = Math.round(parseFloat(amount) * 100);
        const minAmount = 50;
        const finalAmount = Math.max(amountInCents, minAmount);

        const paymentIntent = await stripe.paymentIntents.create({
            amount: finalAmount,
            currency: 'usd',
            customer: customer_id,
            payment_method: saved_card_id,
            description: `Payment for ad: ${ad_title || 'Untitled'}`,
            confirm: true,
            off_session: true,
            automatic_payment_methods: {
                enabled: true,
                allow_redirects: 'never'
            },
            // REMOVE THIS LINE: payment_method_types: ['card']
        });

        return {
            success: true,
            payment_intent_id: paymentIntent.id,
            status: paymentIntent.status,
            amount: amount,
            card_details: paymentIntent.payment_method_details?.card || null
        };
    } catch (error) {
        return {
            success: false,
            error: error.message || "Failed to process payment"
        };
    }
}
async function createPaymentRecord({ ad_id, payment_intent_id, customer_id, payment_method_id, amount, created_by_id, created_by_type, status }) {
    try {
        let cardDetails = {};
        if (payment_method_id) {
            const paymentMethod = await stripe.paymentMethods.retrieve(payment_method_id);
            cardDetails = {
                card_last4: paymentMethod.card.last4,
                card_brand: paymentMethod.card.brand
            };
        }

        await db.payments.create({
            ad_id,
            amount: parseFloat(amount),
            currency: 'usd',
            stripe_payment_id: payment_intent_id,
            stripe_customer_id: customer_id,
            stripe_card_id: payment_method_id,
            payment_method_type: 'card',
            card_last4: cardDetails.card_last4,
            card_brand: cardDetails.card_brand,
            payment_status: status,
            created_by_id,
            created_by_type
        });
    }
    catch (error) {
        console.error("Error creating payment record:", error);
    }
}
async function refundPayment(payment_intent_id) {
    try {
        await stripe.refunds.create({
            payment_intent: payment_intent_id
        });
    } catch (error) {
        console.error("Error refunding payment:", error);
    }
}

module.exports = {
    // Add card method - simplified
    async addCard(req, res) {
        try {
            const { token, created_by_id, created_by_type } = req.body;

            if (!token || !created_by_id || !created_by_type) {
                return res.status(400).json({
                    status: status.ERROR,
                    message: "Token, user ID and type are required",
                    data: {}
                });
            }

            // Check if customer already exists
            let customer;
            const existingCustomer = await db.payments.findOne({
                where: {
                    created_by_id: created_by_id,
                    created_by_type: created_by_type,
                    stripe_customer_id: {
                        [Sequelize.Op.not]: null
                    }
                },
                attributes: ['stripe_customer_id']
            });

            if (existingCustomer && existingCustomer.stripe_customer_id) {
                try {
                    // Add new card to existing customer using payment method
                    const paymentMethod = await stripe.paymentMethods.create({
                        type: 'card',
                        card: { token }
                    });

                    // Attach payment method to customer
                    await stripe.paymentMethods.attach(paymentMethod.id, {
                        customer: existingCustomer.stripe_customer_id
                    });

                    // Save the new payment method as a payment record
                    await db.payments.create({
                        created_by_id,
                        created_by_type,
                        stripe_customer_id: existingCustomer.stripe_customer_id,
                        stripe_card_id: paymentMethod.id,
                        card_last4: paymentMethod.card.last4,
                        card_brand: paymentMethod.card.brand,
                        payment_method_type: 'card',
                        amount: 0,
                        payment_status: 'pending',
                        stripe_payment_id: ''
                    });

                    return res.status(200).json({
                        status: status.SUCCESS,
                        message: "Card added successfully",
                        data: {
                            customer_id: existingCustomer.stripe_customer_id,
                            payment_method_id: paymentMethod.id,
                            saved_card_id: paymentMethod.id, // Add this for clarity
                            card_details: {
                                last4: paymentMethod.card.last4,
                                brand: paymentMethod.card.brand,
                                exp_month: paymentMethod.card.exp_month,
                                exp_year: paymentMethod.card.exp_year
                            }
                        }
                    });
                } catch (error) {
                    return res.status(400).json({
                        status: status.ERROR,
                        message: error.message || "Error adding card",
                        data: {}
                    });
                }
            }

            // If no existing customer, create new customer with payment method
            try {
                // Create payment method first
                const paymentMethod = await stripe.paymentMethods.create({
                    type: 'card',
                    card: { token }
                });

                // Create customer
                customer = await stripe.customers.create({
                    description: `${created_by_type} - ${created_by_id}`
                });

                // Attach payment method to customer
                await stripe.paymentMethods.attach(paymentMethod.id, {
                    customer: customer.id
                });

                // Set as default payment method
                await stripe.customers.update(customer.id, {
                    invoice_settings: {
                        default_payment_method: paymentMethod.id
                    }
                });

                // Save customer and payment method info
                await db.payments.create({
                    created_by_id,
                    created_by_type,
                    stripe_customer_id: customer.id,
                    stripe_card_id: paymentMethod.id,
                    card_last4: paymentMethod.card.last4,
                    card_brand: paymentMethod.card.brand,
                    payment_method_type: 'card',
                    amount: 0,
                    payment_status: 'pending',
                    stripe_payment_id: ''
                });

                return res.status(200).json({
                    status: status.SUCCESS,
                    message: "Card added successfully",
                    data: {
                        customer_id: customer.id,
                        payment_method_id: paymentMethod.id,
                        saved_card_id: paymentMethod.id, // Add this for clarity
                        card_details: {
                            last4: paymentMethod.card.last4,
                            brand: paymentMethod.card.brand,
                            exp_month: paymentMethod.card.exp_month,
                            exp_year: paymentMethod.card.exp_year
                        }
                    }
                });
            } catch (error) {
                return res.status(500).json({
                    status: status.ERROR,
                    message: error.message || "Error adding card",
                    data: {}
                });
            }
        } catch (error) {
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Error adding card",
                data: {}
            });
        }
    },


    async getSavedCards(req, res) {
        try {
            const { created_by_id, created_by_type } = req.body;
            console.log(created_by_id,"created_by_id",created_by_type)
             console.log(!created_by_id || !created_by_type,"condition")

            if (!created_by_id || !created_by_type) {
                return res.status(400).json({
                    status: status.ERROR,
                    message: "User ID and type are required",
                    data: {}
                });
            }

            // Find customer ID first
            const customerRecord = await db.payments.findOne({
                where: {
                    created_by_id,
                    created_by_type,
                    stripe_customer_id: {
                        [Sequelize.Op.not]: null
                    }
                },
                attributes: ['stripe_customer_id']
            });

            if (!customerRecord || !customerRecord.stripe_customer_id) {
                return res.status(200).json({
                    status: status.SUCCESS,
                    message: "No cards found",
                    data: { cards: [] }
                });
            }

            // Get payment methods from Stripe
            const paymentMethods = await stripe.paymentMethods.list({
                customer: customerRecord.stripe_customer_id,
                type: 'card'
            });

            // Get default payment method
            const customer = await stripe.customers.retrieve(customerRecord.stripe_customer_id);
            const defaultPaymentMethodId = customer.invoice_settings?.default_payment_method;

            // Format the response
            const cards = paymentMethods.data.map(pm => ({
                id: pm.id,
                stripe_customer_id: customerRecord.stripe_customer_id,
                card_last4: pm.card.last4,
                card_brand: pm.card.brand,
                exp_month: pm.card.exp_month,
                exp_year: pm.card.exp_year,
                is_default: pm.id === defaultPaymentMethodId
            }));

            return res.status(200).json({
                status: status.SUCCESS,
                message: "Cards retrieved successfully",
                data: { cards }
            });
        } catch (error) {
            console.error("Get saved cards error:", error);
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Error retrieving cards",
                data: {}
            });
        }
    },

    // Process payment for an ad - updated to use Payment Intents API
    async processPayment(req, res) {
        try {
            const {
                card_id,
                customer_id,
                amount,
                ad_id = null,
                created_by_id,
                created_by_type,
                save_card = false
            } = req.body;

            if (!card_id || !customer_id || !amount || !created_by_id || !created_by_type) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Card ID, customer ID, amount, and user information are required",
                    status.ERROR
                );
            }

            try {
                const card = await stripe.paymentMethods.retrieve(card_id);

                if (!card) {
                    return response(
                        res,
                        status.SUCCESSSTATUS,
                        {},
                        "Invalid card. Please use a different card.",
                        status.ERROR
                    );
                }
            } catch (cardError) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Invalid card. Please use a different card.",
                    status.ERROR
                );
            }

            try {
                const amountInCents = Math.round(parseFloat(amount) * 100);
                const minAmount = 50;
                const finalAmount = Math.max(amountInCents, minAmount);

                console.log(`Creating payment intent: ${finalAmount} cents for ad ${ad_id}`);

                const paymentIntent = await stripe.paymentIntents.create({
                    amount: finalAmount,
                    currency: 'usd',
                    customer: customer_id,
                    payment_method: card_id,
                    description: `Payment for ad ID: ${ad_id || 'New Ad'}`,
                    confirm: true,
                    off_session: true,
                    // REMOVE THIS LINE: payment_method_types: ['card'],
                    setup_future_usage: save_card ? 'off_session' : null
                });


                const paymentData = {
                    ad_id: ad_id,
                    amount: parseFloat(amount),
                    currency: 'usd',
                    stripe_payment_id: paymentIntent.id,
                    stripe_customer_id: customer_id,
                    stripe_card_id: card_id,
                    payment_method_type: 'card',
                    card_last4: paymentIntent.payment_method_details?.card?.last4,
                    card_brand: paymentIntent.payment_method_details?.card?.brand,
                    payment_status: paymentIntent.status === 'succeeded' ? 'paid' : 'pending',
                    created_by_id: created_by_id,
                    created_by_type: created_by_type
                };

                if (process.env.STRIPE_SECRET_KEY.includes('_test_') || process.env.STRIPE_SECRET_KEY.includes('sk_test_')) {
                    paymentData.payment_status = 'paid';

                    if (ad_id) {
                        await db.ads.update(
                            { payment_status: 'paid' },
                            { where: { id: ad_id } }
                        );
                    }
                }

                const payment = await db.payments.create(paymentData);

                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {
                        payment_id: payment.id,
                        payment_intent_id: paymentIntent.id,
                        status: paymentIntent.status,
                        amount: paymentIntent.amount / 100,
                        currency: paymentIntent.currency
                    },
                    "Payment processed successfully",
                    status.SUCCESS
                );
            } catch (stripeError) {
                console.error("Stripe error:", stripeError);
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    stripeError.message || "Failed to process payment",
                    status.ERROR
                );
            }
        } catch (error) {
            console.log("Process payment error:", error);
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR || "Internal server error",
                status.ERROR
            );
        }
    },
    // Create a Setup Intent for saving a payment method
    async createSetupIntent(req, res) {
        try {
            const { created_by_id, created_by_type } = req.body;

            if (!created_by_id || !created_by_type) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "User ID and type are required",
                    status.ERROR
                );
            }

            // Check if customer already exists
            let customerId;
            const existingCustomer = await db.payments.findOne({
                where: {
                    created_by_id,
                    created_by_type,
                    stripe_customer_id: {
                        [Sequelize.Op.not]: null
                    }
                },
                attributes: ['stripe_customer_id']
            });

            if (existingCustomer && existingCustomer.stripe_customer_id) {
                customerId = existingCustomer.stripe_customer_id;
            } else {
                // Create a new customer
                const customer = await stripe.customers.create({
                    description: `${created_by_type} - ${created_by_id}`
                });
                customerId = customer.id;

                // Save the new customer
                await db.payments.create({
                    created_by_id,
                    created_by_type,
                    stripe_customer_id: customerId,
                    payment_method_type: 'card',
                    amount: 0,
                    payment_status: 'pending',
                    stripe_payment_id: ''
                });
            }

            // Create a SetupIntent
            const setupIntent = await stripe.setupIntents.create({
                customer: customerId,
                payment_method_types: ['card'],
                usage: 'off_session' // This allows the payment method to be used for future payments
            });

            console.log(`Setup intent created: ${setupIntent.id} for customer: ${customerId}`);

            return response(
                res,
                status.SUCCESSSTATUS,
                {
                    clientSecret: setupIntent.client_secret,
                    setupIntentId: setupIntent.id,
                    customerId: customerId
                },
                "Setup intent created successfully",
                status.SUCCESS
            );
        } catch (error) {
            console.error("Create setup intent error:", error);
            return response(
                res,
                status.SUCCESSSTATUS,
                {},
                error.message || "Error creating setup intent",
                status.ERROR
            );
        }
    },
    // Check payment status - simplified
    async checkPaymentStatus(req, res) {
        try {
            const { charge_id } = req.query;

            if (!charge_id) {
                return res.status(400).json({
                    status: status.ERROR,
                    message: "Charge ID is required",
                    data: {}
                });
            }

            const charge = await stripe.charges.retrieve(charge_id);

            return res.status(200).json({
                status: status.SUCCESS,
                message: "Payment status retrieved",
                data: {
                    charge_id: charge.id,
                    amount: charge.amount / 100,
                    status: charge.status
                }
            });
        } catch (error) {
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Error checking payment status",
                data: {}
            });
        }
    },

    async createAdWithPayment(req, res) {
        console.log('createAdWithPayment...');
        try {
            // Validate request
            const validation = await adsValidation.addAdsValidation(req);

            if (validation.error) {
                return response(res, status.SUCCESSSTATUS, {}, validation.error.details[0].message, status.ERROR);
            }

            const {
                payment_method_id,
                save_card,
                daily_budget,
                created_by_id,
                created_by_type,
                ad_title,
                start_date,
                end_date,
                renew_flag=false
            } = req.body;

            // Skip payment process for created_by_id = 3 (move this before payment validation)
            const skipPaymentClientIds = process.env.ADVERTISER_FEATURES_SKIP_PAYMENT_CLIENT_ID?.split(',').map(id => Number(id.trim()));        
            if (skipPaymentClientIds.includes(Number(created_by_id))) {
                console.log('Skipping payment for client ID:', created_by_id);
                // Create the ad directly without payment
                const adRequest = { ...req };
                adRequest.body.payment_status = 'paid'; // Mark as paid without actual payment
                const adResult = await adsService.add(adRequest, res);

                if (adResult === 0) {
                    return response(res, status.SUCCESSSTATUS, {}, "Campaign not found", status.ERROR);
                } else if (adResult === -1) {
                    return response(res, status.SUCCESSSTATUS, {}, "Ad with this title already exists", status.ERROR);
                }

                const msg = renew_flag ? message.ADRENEWED : message.ADCREATED;
                return response(res, status.SUCCESSSTATUS, {
                    ad: adResult,
                    payment: {
                        amount: daily_budget,
                        payment_intent_id: 'skipped-payment-id',
                        card_saved: false
                    }
                }, msg, status.SUCCESS);
            }

            // For other users, validate payment information
            if (!payment_method_id) {
                return response(res, status.SUCCESSSTATUS, {}, "Payment method ID is required", status.ERROR);
            }

            // Calculate total amount
            const startDateObj = new Date(start_date);
            const endDateObj = new Date(end_date);
            const days = Math.ceil((endDateObj - startDateObj) / (1000 * 60 * 60 * 24)) + 1;
            // const totalAmount = daily_budget * days;
            const totalAmount = daily_budget;

            // Get or create customer
            const customerId = await getOrCreateCustomer(created_by_id, created_by_type);
            
            // Process payment
            const paymentResult = await processPayment({
                payment_method_id,
                customer_id: customerId,
                amount: totalAmount,
                description: `Payment for ad: ${ad_title}`,
                save_card: save_card === 'true' || save_card === true
            });

            if (!paymentResult.success) {
                return response(res, status.SUCCESSSTATUS, {}, paymentResult.error, status.ERROR);
            }

            // Create the ad
            const adRequest = { ...req };
            adRequest.body.payment_status = 'paid';
            adRequest.body.stripe_payment_id = paymentResult.payment_intent_id;
            const adResult = await adsService.add(adRequest, res);

            if (adResult === 0) {
                await refundPayment(paymentResult.payment_intent_id);
                return response(res, status.SUCCESSSTATUS, {}, "Campaign not found", status.ERROR);
            } else if (adResult === -1) {
                await refundPayment(paymentResult.payment_intent_id);
                return response(res, status.SUCCESSSTATUS, {}, "Ad with this title already exists", status.ERROR);
            }

            // Create payment record
            await createPaymentRecord({
                ad_id: adResult.id,
                payment_intent_id: paymentResult.payment_intent_id,
                customer_id: customerId,
                payment_method_id,
                amount: totalAmount,
                created_by_id,
                created_by_type,
                status: 'paid'
            });
            const msg = renew_flag ? message.ADRENEWED : message.ADCREATED;

            return response(res, status.SUCCESSSTATUS, {
                ad: adResult,
                payment: {
                    amount: totalAmount,
                    payment_intent_id: paymentResult.payment_intent_id,
                    card_saved: save_card && paymentResult.card_saved
                }
            }, msg, status.SUCCESS);

        } catch (error) {
            return response(res, status.INTERNALSERVERERRORSTATUS, {}, "Internal server error", status.ERROR);
        }
    },
    // Create ad with payment - streamlined
    // Create ad with payment - updated to use Payment Intents API
    async addAd(req, res) {
        try {
            // Validate request
            const validation = await adsValidation.addAdsValidation(req);
            if (validation.error) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    validation.error.details[0].message,
                    status.ERROR
                );
            }

            // Convert save_card string to boolean if needed
            if (req.body.save_card === 'true') {
                req.body.save_card = true;
            } else if (req.body.save_card === 'false') {
                req.body.save_card = false;
            }

            // Check if payment information is provided
            const {
                stripe_payment_id,
                saved_card_id,
                customer_id,
                daily_budget,
                created_by_id,
                created_by_type,
                ad_title,
                renew_flag=false
            } = req.body;

            // These variables will be used later in the payment creation
            let card_last4 = req.body.card_last4;
            let card_brand = req.body.card_brand;
            const payment_method_type = req.body.payment_method_type || 'card';

            // Payment is required for all ads regardless of budget
            if (!stripe_payment_id && (!saved_card_id || !customer_id)) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Payment information is required to publish an ad",
                    status.ERROR
                );
            }

            // Verify the payment ID if provided or process a new payment
            let processedPaymentId = stripe_payment_id;
            let paymentStatus = 'pending';

            if (stripe_payment_id) {
                try {
                    // Verify the payment ID with Stripe
                    const payment = await stripe.paymentIntents.retrieve(stripe_payment_id);

                    if (payment.status !== 'succeeded') {
                        return response(
                            res,
                            status.SUCCESSSTATUS,
                            {},
                            "Invalid payment. Please provide a valid payment",
                            status.ERROR
                        );
                    }

                    paymentStatus = 'paid';

                    // Use card details from the payment intent if available
                    if (payment.payment_method_details?.card) {
                        card_last4 = payment.payment_method_details.card.last4 || card_last4;
                        card_brand = payment.payment_method_details.card.brand || card_brand;
                    }
                } catch (error) {
                    return response(
                        res,
                        status.SUCCESSSTATUS,
                        {},
                        "Invalid payment ID. Please provide a valid payment",
                        status.ERROR
                    );
                }
            } else if (saved_card_id && customer_id && parseFloat(daily_budget) > 0) {
                // Process a new payment using the saved card
                const paymentResult = await processPaymentForAd({
                    saved_card_id,
                    customer_id,
                    amount: daily_budget,
                    ad_title,
                    created_by_id,
                    created_by_type
                });

                if (!paymentResult.success) {
                    return response(
                        res,
                        status.SUCCESSSTATUS,
                        {},
                        paymentResult.error || "Failed to process payment",
                        status.ERROR
                    );
                }

                processedPaymentId = paymentResult.payment_intent_id;
                paymentStatus = paymentResult.status === 'succeeded' ? 'paid' : 'pending';

                // Use card details from the payment result if available
                if (paymentResult.card_details) {
                    card_last4 = paymentResult.card_details.last4 || card_last4;
                    card_brand = paymentResult.card_details.brand || card_brand;
                }
            }

            // Process the rest of the function as normal
            // Add ad - but remove payment fields from the request
            // Create a new request object without payment-related fields
            const adRequest = { ...req };

            // Remove payment-related fields from the request body
            adRequest.body = { ...req.body };
            delete adRequest.body.stripe_payment_id;
            delete adRequest.body.stripe_customer_id;
            delete adRequest.body.stripe_card_id;
            delete adRequest.body.card_last4;
            delete adRequest.body.card_brand;
            delete adRequest.body.payment_method_type;
            delete adRequest.body.payment_status;
            delete adRequest.body.save_card;

            // Add payment status to the request
            adRequest.body.payment_status = paymentStatus;
            adRequest.body.stripe_payment_id = processedPaymentId;

            const result = await adsService.add(adRequest, res);
            if (result === 0) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNNOTFOUND || "Campaign not found",
                    status.ERROR
                );
            } else if (result === -1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Ad with this title already exists for this campaign",
                    status.ERROR
                );
            } else {
                // Create a payment record in the payments table
                if (processedPaymentId || (saved_card_id && customer_id)) {
                    // Create payment record
                    const paymentData = {
                        ad_id: result.id,
                        amount: parseFloat(daily_budget) || 0,
                        currency: 'usd',
                        stripe_payment_id: processedPaymentId,
                        stripe_customer_id: customer_id || null,
                        stripe_card_id: saved_card_id || null,
                        payment_method_type: payment_method_type,
                        card_last4: card_last4 || null,
                        card_brand: card_brand || null,
                        payment_status: paymentStatus,
                        created_by_id: created_by_id,
                        created_by_type: created_by_type
                    };

                    // IMPORTANT: Force payment status to 'paid' in test environment
                    if (process.env.STRIPE_SECRET_KEY.includes('_test_') || process.env.STRIPE_SECRET_KEY.includes('sk_test_')) {
                        paymentStatus = 'paid';
                        paymentData.payment_status = 'paid';

                        // Update the ad record directly
                        await db.ads.update(
                            { payment_status: 'paid' },
                            { where: { id: result.id } }
                        );

                        // Update the result object
                        result.payment_status = 'paid';
                    }

                    // Save payment record
                    await db.payments.create(paymentData);
                }

                const msg = renew_flag ? message.ADRENEWED : message.ADCREATED;

                // ADCREATED
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    result,
                    msg,
                    status.SUCCESS
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR || "Internal server error",
                status.ERROR
            );
        }
    },
    // Process payment with saved card - updated to use Payment Intents API
    async processPaymentWithSavedCard(req, res) {
        try {
            const { card_id, customer_id, amount, description } = req.body;

            if (!customer_id || !amount) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    "Customer ID and amount are required",
                    status.ERROR
                );
            }

            try {
                const amountInCents = Math.round(parseFloat(amount) * 100);
                const minAmount = 50;
                const finalAmount = Math.max(amountInCents, minAmount);

                const paymentIntent = await stripe.paymentIntents.create({
                    amount: finalAmount,
                    currency: 'usd',
                    customer: customer_id,
                    payment_method: card_id,
                    description: description || 'Payment for ad',
                    confirm: true,
                    off_session: true,
                    automatic_payment_methods: {
                        enabled: true,
                        allow_redirects: 'never'
                    },
                    // REMOVE THIS LINE: payment_method_types: ['card']
                });

                if (paymentIntent.status === 'succeeded') {
                    return response(
                        res,
                        status.SUCCESSSTATUS,
                        {
                            payment_intent_id: paymentIntent.id,
                            status: paymentIntent.status,
                            amount: paymentIntent.amount / 100,
                            currency: paymentIntent.currency
                        },
                        "Payment processed successfully",
                        status.SUCCESS
                    );
                } else if (paymentIntent.status === 'requires_action') {
                    return response(
                        res,
                        status.SUCCESSSTATUS,
                        {
                            payment_intent_id: paymentIntent.id,
                            status: paymentIntent.status,
                            client_secret: paymentIntent.client_secret
                        },
                        "Payment requires additional authentication",
                        status.ERROR
                    );
                } else {
                    return response(
                        res,
                        status.SUCCESSSTATUS,
                        {
                            payment_intent_id: paymentIntent.id,
                            status: paymentIntent.status
                        },
                        "Payment processing failed",
                        status.ERROR
                    );
                }
            } catch (stripeError) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    stripeError.message || "Failed to process payment",
                    status.ERROR
                );
            }
        } catch (error) {
            return response(
                res,
                status.INTERNALSERVERERRORSTATUS,
                {},
                message.INTERNALSERVERERROR || "Internal server error",
                status.ERROR
            );
        }
    },
    // Update card method
    async updateCard(req, res) {
        try {
            const { new_payment_method_id, card_id, created_by_id, created_by_type } = req.body;

            if (!new_payment_method_id || !card_id || !created_by_id || !created_by_type) {
                return res.status(400).json({
                    status: status.ERROR,
                    message: "New payment method ID, card ID, user ID and type are required",
                    data: {}
                });
            }

            // Check if customer exists
            const customerRecord = await db.payments.findOne({
                where: {
                    created_by_id,
                    created_by_type,
                    stripe_customer_id: {
                        [Sequelize.Op.not]: null
                    }
                },
                attributes: ['stripe_customer_id']
            });

            if (!customerRecord || !customerRecord.stripe_customer_id) {
                return res.status(400).json({
                    status: status.ERROR,
                    message: "Customer not found",
                    data: {}
                });
            }

            try {
                // Verify the old card exists and belongs to this customer
                const existingCard = await stripe.paymentMethods.retrieve(card_id);

                if (!existingCard || existingCard.customer !== customerRecord.stripe_customer_id) {
                    return res.status(400).json({
                        status: status.ERROR,
                        message: "Original card not found or doesn't belong to this customer",
                        data: {}
                    });
                }

                // Verify the new payment method exists
                const newPaymentMethod = await stripe.paymentMethods.retrieve(new_payment_method_id);
                
                if (!newPaymentMethod) {
                    return res.status(400).json({
                        status: status.ERROR,
                        message: "New payment method not found",
                        data: {}
                    });
                }

                // Attach the new payment method to the customer if not already attached
                if (!newPaymentMethod.customer) {
                    await stripe.paymentMethods.attach(new_payment_method_id, {
                        customer: customerRecord.stripe_customer_id
                    });
                }

                // Check if the old card was the default payment method
                const customer = await stripe.customers.retrieve(customerRecord.stripe_customer_id);
                const wasDefault = customer.invoice_settings?.default_payment_method === card_id;

                // Detach the old payment method
                await stripe.paymentMethods.detach(card_id);

                // If the old card was default, set the new one as default
                if (wasDefault) {
                    await stripe.customers.update(customerRecord.stripe_customer_id, {
                        invoice_settings: {
                            default_payment_method: new_payment_method_id
                        }
                    });
                }

                // Update payment record in database
                await db.payments.update(
                    {
                        stripe_card_id: new_payment_method_id,
                        card_last4: newPaymentMethod.card.last4,
                        card_brand: newPaymentMethod.card.brand,
                        payment_status: 'updated',
                        updated_at: new Date()
                    },
                    {
                        where: {
                            created_by_id,
                            created_by_type,
                            stripe_card_id: card_id
                        }
                    }
                );

                return res.status(200).json({
                    status: status.SUCCESS,
                    message: "Card updated successfully",
                    data: {
                        customer_id: customerRecord.stripe_customer_id,
                        payment_method_id: new_payment_method_id,
                        card_details: {
                            last4: newPaymentMethod.card.last4,
                            brand: newPaymentMethod.card.brand,
                            exp_month: newPaymentMethod.card.exp_month,
                            exp_year: newPaymentMethod.card.exp_year
                        }
                    }
                });

            } catch (error) {
                console.error("Error updating payment method:", error);
                return res.status(400).json({
                    status: status.ERROR,
                    message: error.message || "Error updating card",
                    data: {}
                });
            }
        } catch (error) {
            console.error("Update card error:", error);
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Internal server error",
                data: {}
            });
        }
    },
    async deleteAd(req, res) {
        try {
            const { id, type } = req.body;

            if (!id || !type) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.ADSNOTFOUND,
                    status.ERROR
                );
            }
            const result = await adsService.deleteAd(req, res);
            if (result == 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.ADSDELETED,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.NOADS,
                    status.ERROR
                );
            }
        } catch (error) {
            console.error("Update card error:", error);
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Internal server error",
                data: {}
            });
        }
    },
    async deleteCampaign(req, res) {
        try {
            const { id, type } = req.body;

            if (!id || !type) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.CAMPAIGNNOTFOUND,
                    status.ERROR
                );
            }
            const result = await adsService.deleteCampaign(req, res);
            if (result == 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.CAMPAIGNDELETED,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.NOCAMPAIGN,
                    status.ERROR
                );
            }
        } catch (error) {
            console.error("Update card error:", error);
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Internal server error",
                data: {}
            });
        }
    },
    async updatePauseStatus(req, res) {
        try {
            const { id, type, flag } = req.body;

            if (!id || !type || flag === 'undefined' || flag === null) {
                return response(
                    res,
                    status.BADREQUESTCODE,
                    {},
                    message.PAUSEMESSINGREQUIRED,
                    status.ERROR
                );
            }
            const result = await adsService.updatePauseStatus(req, res);            
            const msg = flag === true ? message.ADSPAUSED : message.ADSRESUMED;

            if (result == 1) {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    msg,
                    status.SUCCESS
                );
            } else {
                return response(
                    res,
                    status.SUCCESSSTATUS,
                    {},
                    message.NOADS,
                    status.ERROR
                );
            }
        } catch (error) {
            console.error("Update pause status error:", error);
            return res.status(500).json({
                status: status.ERROR,
                message: error.message || "Internal server error",
                data: {}
            });
        }
    }

};
