const cmsMessage = {
	SUCCESS: 'Success',
	EMAILNOTFOUND: 'There is no account found with this email.',
	<PERSON><PERSON><PERSON>NSUCCESS: 'Welcome back, you are now logged in.',
	LOGOUTSUCCESS: 'You have been logged out.',
	OTPDIDNOTMATCH: 'Wrong OTP inserted.',
	PA<PERSON><PERSON><PERSON><PERSON>HANGED: 'Your password changed successfully.',
	PASSCODECHANGED: 'Your passcode changed successfully.',
	PASSCODESTATUSCHANGED: 'Your passcode status changed successfully.',
	PASSWORDVERIFY: 'Your password has been verified.',
	PASSCODEVERIFY: 'Passcode verify successfully.',
	VALIDPASSCODE: 'Please enter valid Passcode.',
	PA<PERSON><PERSON><PERSON><PERSON>HANGEDDATE: 'Your password changed date',
	PA<PERSON><PERSON><PERSON><PERSON><PERSON>ET: 'Your password reset successfully.',
	INCORRECTPASSWORD: 'Invalid password, please try again.',
	INCORRECTOLDPASSWORD: 'Old password is incorrect.',
	INCORRECTOLDPASSCODE: 'Old passcode is incorrect.',
	CH<PERSON><PERSON><PERSON>SSCODESTATUSFIRST: 'Change passcode status first.',
	INCORRECTCURRENTPASSWORD: 'Current password is incorrect.',
	<PERSON><PERSON><PERSON>SENTSUCCESSFULLY:
		'An email has been sent, please check your inbox or spam/junk folder.',
	WRONGOTPINSERTED: 'Wrong otp inserted.',
	WRONGMFAINSERTED: 'Invalid code, please try again.',
	OTPVERIFIED: 'OTP verified.',
	ACCOUNTLINKSUCCESS:
		'Your MyTab Venue account has been successfully linked to your Management Portal.',
	SOMETHISFISHY: 'Something is fishy.',
	MFASENT:
		'An email has been sent, please check your inbox or spam/junk folder.',
	SOMETHINGWENTWRONG:
		"We're sorry, something went wrong. Please refresh your screen or contact us for support.",
	INTERNALSERVERERROR:
		"We're sorry, some internal server error occurred. Please try after sometime or contact us for support.",
	UNAUTHORIZEDUSER: 'You are unauthorized',
	TOKENREQUIRED: 'Token is required',
	ADMINUPDATED: 'Profile updated successfully',
	TOKEN_VALID: 'Token is valid',
	TOKEN_EXPIRED: 'Your request has been expired. Please try again.',
	PASSWORD_RESET: 'Your password has been reset.',
	PROFILERETRIVED: 'Profile retrived successfully',
	USERNOTFOUND: 'User not found',
	USERINACTIVE: 'USER is inactive',
	USERBLOCKED: 'User is blocked',
	GETANALYTICS: 'GetAnalytics successfully',
	USERNOTAPPROVED: 'User not approved',
	ACCOUNTBLOCKED:
		'You account has been blocked. Please contact to Admin to unblock your account.',
	TOKENLIST: 'Device list fetch successfully',
	CAMPAIGNLIST: 'Campaign list fetch successfully',
	INVALIDMISSINGTYPE: 'Invalid or missing type (must be "venue" or "advertiser")',
	ADMINNOTFOUND: 'Admin not found',
	CAMPAIGNNOTFOUND: 'Campaign not found',
	ADCREATED: 'Ad submitted for approval',
	ADRENEWED: 'Ad renewed successfully',
	TASKCREATED: 'Task created sucessfully',
	TASKNOTEXIST: 'Task does not exist',
	TASKDELETED: 'Task deleted successfully',
	TASKUPDATED: 'Task updated successfully',
	TASKLISTFETCHSUCCESSFULLY: 'Todo list fetch successfully',
	PROFILEUPDATED: 'Profile updated successfully',
	INVALIDCREDENTIAL: 'There is no account found with these credentials.',
	EMAILALEREADYEXIST: 'This email already exists.',
	CAMPAIGNALEREADYEXIST: 'This campaign already exists.',
	REGISTERSUCCESS: 'Congratulations, you have created your Management Portal.',
	ADVERTISERSUCCESS: 'Your advertiser account has been created and submitted for review by our team.',
	CAMPAIGNSUCCESS: 'Campaign created sucessfully.',
	NOTFOUND: 'Not found',
	MENUITEAMDDED: 'Menu item added successfully',
	PRODUCTDELETED: 'Product deleted Successfully',
	CODEFOUND: 'Code successfully fetched',
	INVALIDMOBILENUMBER: 'INVALID Mobile number',
	INVALIDABNACNNUMBER: 'INVALID ABN/ACN number',
	PRODUCTTAXFETCHSUCCESSFULLY: 'Product tax fetched successfully',
	ACCOUNTDELETEFORMFETCHSUCCESSFULLY:
		'Account delete form fetched successfully',
	UPDATEDSUCCESSFULLY: 'Updated successfully',
	FOODOPTIONLIST: 'Food option list fetch successfully',
	MENUITEAMUPDATED: 'Menu item updated successfully',
	LISTFETCHEDSUCESSFULLY: 'List fetch successfully',
	MENUITEMADDED: 'Menu item added successfully',
	TAGIDNOTFOUND: 'Tag ID not found.',
	PRODUCTNOTFOUND: 'Product not found',
	PRODUCTVARIANTNOTEXIST: 'Product variant does not exist',
	PRODUCTVARIANTDELETEDSUCCESSFULLY: 'Product variant deleted successfully',
	DELETEPRODUCTEXTRASSUCCESSFULLY: 'Product extra deleted successfully',
	PRODUCTEXTRANOTEXIST: 'Product extra does not exist',
	PRODUCTFETCHSUCCESSFULLY: 'Product data fetched successfully',
	INVALIDTIMESLOT:
		"Invalid Time-Slot! The opening hours cann't be set after closing hours.",
	TIMESLOTUPDATED: 'Timeslot updated sucessfully',
	TIMESLOTADDED: 'Timeslot added sucessfully',
	TIMESLOTALREADYEXIST: 'Time-Slot already exists! Enter another one',
	PRODUCTSEQUENCEUPDATEDSUCESSFULLY: 'Product sequence updated successfully',
	INCORRECTCURRENTPRODUCT: 'Incorrect products provided',
	TIMEOUTFORVENUE:
		"The selected time is out of Venue's operating Hours. Please try again.",
	REQUIREFULLWEEKDATA:
		"You'll need to fill all the week day's data to proceed.",
	OPERATINGHOURSUPDATESUCCESSFULLY: 'Operating hours updated successfully',
	OPENINGHOURSFETCHSUCCESSFULLY: 'Opening hours fetched successfully',
	OPENINGHOURSUPDATESUCCESSFULLY: 'Opening hours updated successfully',
	CATEGORYHOURSUPDATESUCCESSFULLY: 'Category hours updated successfully',
	HOURSLISTFETCHEDSUCESSFULLY: 'Operating hours retrieved successfully.',
	HOURSLISTOUTSIDEOPENINGHOURS:
		"The selected time is out of Venue's operating Hours. Please try again.",
	MISSINGHOURS: "Venue's Operating Hours are missing.",
	LOCATIONADDED: 'Pick up location added successfully.',
	CATEGORYASSIGNALREADY:
		'This category is already assign to other pickup location.',
	LOCATIONSFETCHSUCCESSFULLY: 'Pickup location list fetch successfully.',
	LOCATIONUPDATED: 'Location updated successfully',
	LOCATIONDELETED: 'Pick up location deleted successfully',
	NOLOCATIONFOUND: 'No pick up location fond with given data',
	CATEGORYLISTFETCHEDSUCESSFULLY: 'Category List fetch successfully',
	SEQUENCEUPDATEDSUCCESSFULLY: 'Bar sequence updated successfully',
	INCORRECTSEQUNCE: "'Incorrect sub categories provided!'",
	COUPONCODEALREADY: 'Coupon code or name already in use.',
	MAXLIMITREACHED:
		'Your venues promo codes can only be set to a maximum of 80% off due merchant fees. Thank you.',
	PROMOCODEADDEDSUCCESSFULLY: 'Promcode added successfully',
	COUPONNOTFOUND: 'Coupon not found',
	COUPONDELETED: 'Coupon deleted successfully',
	VENUECONNECTSUCCESSFULLY: 'Venue connected successfully.',
	VENUENOTFOUND: 'Incorrect venue email address.',
	VENUEALREADYCONNECTED: 'Venue Already connected with Your account.',
	VENUEDATAFETCHSUCCESSFULLY: 'Venue data fetch successfully.',
	TIMEZONEFETCHSUCCESSFULLY: 'Timezone data fetch successfully.',
	VENUEUPDATEDSUCESSFULLY: 'Venue details updated successfully.',
	VENUEMOBILEUPDATEDSUCESSFULLY: 'Venue mobile updated successfully.',
	CONNECTVENUENOTALLOWED:
		'In your subscriptions plan you are only allowed to connect one venue in your account.',
	VENUEIMAGEREQUIRED: 'Venue image required.',
	VENUEEMAILALEREADYEXIST:
		'Venue aleready exist with this email. Please use different email.',
	VENUEADDEDSUCESSFULLY: 'Venue created successfully.',
	CONTACTUSMAILSENTSUCESSFULLY:
		'Thank you for contacting us, a team member will be in contact shortly.',
	EMAILFAILED: 'Email Failed. Please try again.',
	PRODUCTSEQUENCEUPDATEDSUCESSFULLY: 'Product sequence updated successfully.',
	INCORRECTCURRENTPRODUCT: 'Incorrect products provided.',
	ACTIVEHOURSFETCHED: 'Active hours retrieved successfully.',
	INVALIDSUBCATEGORY: 'Invalid subcategory provided',
	PRODUCTIMAGEREQUIRED: 'Product image required',
	SUBSCRPTIONADD: 'Subscription added successfully',
	MYVENUELISTFETCH: 'My venue list fetched successfully',
	PROFILEIMAGEREQUIRED: 'Please upload a profile image.',
	VENUERELATIONNOTEXIST: 'Venue relation does not exist',
	VENUEDELETEDSUCESSFULLY: 'Venue deleted successfully',
	ORDERNOTFOUND: 'Order not found',
	ITEMSUMMARYREPORTFETCHEDSUCCESSFULLY:
		'item summary report fetched successfully',
	SUBCATEGORY_LIST: 'Subcategory list fetch successfully',
	DATAFETCHSUCESSFULLY: 'Data fetch successfully',
	STATUSCHAGESUCCESSFULLY: 'Status change successfully',
	DOCKETFEATUREDEACTIVATED: 'Docket Printing Feature deactivated',
	DOCKETFEATUREACTIVATED: 'Docket Printing Feature activated',
	SEQUENCEUPDATEDSUCCESSFULLY: 'Sequence updated successfully',
	SUBSCRIPTIONCRESTED: 'Subscription created successfully',
	POSFEATUREDEACTIVATED: 'POS Feature deactivated',
	POSFEATUREACTIVATED: 'POS Feature activated',
	WEBHOOKSAVESUCCESSFULLY: 'Webhook save successfully',
	INCORRECTSEQUNCE: 'Incorrect sequence provided',
	ORDER_HISTORY_LIST_SUCCESSFULL: 'Order list retrieve successfully!',
	CODEFOUND: 'Code successfully fetched',
	ORDER_DETAILS_FETCHED_SUCESSFULLY: 'Order details fetch successfully',
	ORDER_REFUNDED_SUCCESSFULLY: 'Order refunded successfully',
	ORDER_ITEM_STATUS_UPDATED_SUCCESSFULLY: 'Order updated successfully',
	READYFORPICKUPALERTSENT: 'Ready for pickup alert sent successfully',
	ORDER_INTOXICATED_SUCCESSFULLY: 'Order intoxicated successfully',
	ORDER_DATA_EXPORTED_SUCCESSFULLY: 'Order history data exported successfully.',
	DATA_EXPORTED_SUCCESSFULLY: 'Data exported successfully.',
	ORDERNOTFOUND: 'Order details not found',
	DELETEITEMHOURSSUCCESSFULLY: 'Time-slot deleted successfully',
	BARNOTFOUND: 'Bar not found.',
	NODATAFOUND: 'No data found.',
	SERVICETYPEFETCHEDSUCCESSFULLY: 'Service Type fetched successfully.',
	SERVICETYPEUPDATEDSUCCESSFULLY: 'Service Type updated successfully.',
	TAXALREADYEXIST: 'Surcharge with same name is already in use.',
	TAXADDEDSUCESSFULLY: 'Surcharge added successfully.',
	TAXFETCHEDSUCESSFULLY: 'All surcharges retrieved successfully.',
	NOTAXFOUND: 'No surcharge record found for the Venue.',
	TAXUPDATEDSUCESSFULLY: 'Surcharge updated successfully',
	TAXDELETEDSUCCESSFULLY: 'Surcharge deleted successfully.',
	WAITTIMEGETSUCCESSFULLY: 'Wait time retrieved successfully.',
	WAITTIMEUPDATEDSUCCESSFULLY: 'Wait time updated successfully.',
	NOWAITTIMEFOUND:
		'No wait time found for this sub category. Please add the kitchen time slot first.',
	VENUEISOPENCANNOTUPDATE:
		"Your venue is still open, updating a product's service type is only available outside your venue's opening hours.",
	STRIPEACCOUNTINFORMATION: 'Stripe banking details fetched successfully.',
	CREATESTRIPEACCOUNT:
		'Please fill in the Stripe verification document to connect your banking details to your MyTab Venue account.',
	COMPLETESTRIPEACCOUNT: 'Stripe account had linked successfully.',
	CUSTOMERDETAILSFETCHED: 'Customer details fetched successfully.',
	SEGMENTDETAILSFETCHED: 'Segment details fetched successfully.',
	VENUE_ISOPEN:
		"Your venue is still open, updating your service type is only available outside your venue's opening hours.",
	SUBCATEGORYIDSAME:
		'Parent sub category ID and Child sub category ID can not be same.',
	SUBCATEGORYNOTFOUND: 'sub category not found.',
	SUBCATEGORYLINKEXIST: 'Upsell category already exists.',
	SUBCATEGORYLINKNOTFOUND: 'Upsell category does not exists.',
	SUBCATEGORYLINKADDED: 'Upsell category linked successfully.',
	SUBCATEGORYLINKUPDATED: 'Upsell category updated successfully.',
	SUBCATEGORYUNLINKED: 'Upsell category un-linked successfully',
	ADSNOTFOUND: 'Ads ID and type are required',
	CAMPAIGNNOTFOUND: 'Campaign ID and type are required',
	ADSDELETED: 'Ads deleted successfully.',
	CAMPAIGNDELETED: 'Campaign deleted successfully.',
	NOADS: 'No ads found.',
	NOCAMPAIGN: 'No campaign found.',
	PAUSEMESSINGREQUIRED: 'Missing required fields: id, type, or flag',
	ADSPAUSED: 'Ad paused successfully',
	ADSRESUMED: 'Ad resumed successfully',
};

module.exports = { cmsMessage };
